#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
雪球反爬虫解决方案测试脚本
测试增强的acw_sc_v2参数生成和反调试绕过功能
"""

import execjs
import time
import hashlib
import json
import random
from pathlib import Path

class XueqiuAntiCrawlingTester:
    """雪球反爬虫测试器"""
    
    def __init__(self):
        self.js_context = None
        self.test_results = []
        
    def load_js_context(self):
        """加载JavaScript执行环境"""
        try:
            # 获取当前脚本目录
            current_dir = Path(__file__).parent
            
            # 加载test.js
            test_js_path = current_dir / 'test.js'
            if test_js_path.exists():
                with open(test_js_path, 'r', encoding='utf-8') as f:
                    test_js = f.read()
            else:
                test_js = "function getMd5(url) { return 'test_md5'; }"
            
            # 加载acw_sc_v2.js
            acw_js_path = current_dir / 'acw_sc_v2.js'
            if acw_js_path.exists():
                with open(acw_js_path, 'r', encoding='utf-8') as f:
                    acw_js = f.read()
            else:
                raise FileNotFoundError("acw_sc_v2.js not found")
            
            # 合并JavaScript代码
            all_js = test_js + '\n' + acw_js
            self.js_context = execjs.compile(all_js)
            print("✓ JavaScript context loaded successfully")
            return True
            
        except Exception as e:
            print(f"✗ Failed to load JavaScript context: {e}")
            return False
    
    def test_acw_sc_v2_generation(self):
        """测试acw_sc_v2参数生成"""
        print("\n=== Testing acw_sc_v2 Generation ===")
        
        if not self.js_context:
            print("✗ JavaScript context not available")
            return False
        
        # 测试参数
        test_params = [
            'FA6AEB89B2318F527AD3AE807660BD7BCE67DDFA',
            '1234567890ABCDEF1234567890ABCDEF12345678',
            hashlib.md5(str(time.time()).encode()).hexdigest().upper(),
        ]
        
        strategies = ['default', 'alt', 'offset', 'dynamic']
        
        success_count = 0
        total_tests = len(test_params) * len(strategies)
        
        for param in test_params:
            print(f"\nTesting with parameter: {param[:20]}...")
            
            for strategy in strategies:
                try:
                    result = self.js_context.call('get_acw_sc_v2', param, strategy)
                    if result and len(result) > 10:
                        print(f"  ✓ Strategy '{strategy}': {result[:20]}...")
                        success_count += 1
                    else:
                        print(f"  ✗ Strategy '{strategy}': Invalid result")
                except Exception as e:
                    print(f"  ✗ Strategy '{strategy}': {e}")
        
        # 测试多策略函数
        print(f"\nTesting multi-strategy function...")
        try:
            result = self.js_context.call('get_acw_sc_v2_multi', test_params[0])
            if result and len(result) > 10:
                print(f"  ✓ Multi-strategy: {result[:20]}...")
                success_count += 1
            else:
                print(f"  ✗ Multi-strategy: Invalid result")
        except Exception as e:
            print(f"  ✗ Multi-strategy: {e}")
        
        success_rate = (success_count / (total_tests + 1)) * 100
        print(f"\nSuccess rate: {success_rate:.1f}% ({success_count}/{total_tests + 1})")
        
        self.test_results.append({
            'test': 'acw_sc_v2_generation',
            'success_rate': success_rate,
            'passed': success_rate >= 80
        })
        
        return success_rate >= 80
    
    def test_anti_debug_bypass(self):
        """测试反调试绕过"""
        print("\n=== Testing Anti-Debug Bypass ===")
        
        if not self.js_context:
            print("✗ JavaScript context not available")
            return False
        
        try:
            # 测试debugger语句是否被正确绕过
            test_code = """
            function testDebugger() {
                debugger;
                return 'success';
            }
            testDebugger();
            """
            
            result = self.js_context.eval(test_code)
            if result == 'success':
                print("✓ Debugger bypass working correctly")
                self.test_results.append({
                    'test': 'anti_debug_bypass',
                    'passed': True
                })
                return True
            else:
                print("✗ Debugger bypass failed")
                return False
                
        except Exception as e:
            print(f"✗ Anti-debug test failed: {e}")
            self.test_results.append({
                'test': 'anti_debug_bypass',
                'passed': False
            })
            return False
    
    def test_device_fingerprint(self):
        """测试设备指纹生成"""
        print("\n=== Testing Device Fingerprint ===")
        
        try:
            import platform
            import uuid
            
            # 模拟设备指纹生成
            system_info = f"{platform.system()}-{platform.machine()}-{platform.processor()}"
            mac_address = ':'.join(['{:02x}'.format((uuid.getnode() >> elements) & 0xff) 
                                   for elements in range(0,2*6,2)][::-1])
            
            fingerprint_data = {
                'screen': {'width': 1920, 'height': 1080, 'colorDepth': 24},
                'timezone': -480,
                'language': 'zh-CN',
                'platform': 'MacIntel',
                'userAgent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36',
                'webgl': 'ANGLE (Apple, Apple M1 Pro, OpenGL 4.1)',
                'canvas': hashlib.md5(system_info.encode()).hexdigest()[:16],
                'audio': hashlib.md5(mac_address.encode()).hexdigest()[:16]
            }
            
            fingerprint = hashlib.md5(json.dumps(fingerprint_data, sort_keys=True).encode()).hexdigest()
            
            if len(fingerprint) == 32:
                print(f"✓ Device fingerprint generated: {fingerprint[:16]}...")
                self.test_results.append({
                    'test': 'device_fingerprint',
                    'passed': True
                })
                return True
            else:
                print("✗ Invalid device fingerprint")
                return False
                
        except Exception as e:
            print(f"✗ Device fingerprint test failed: {e}")
            self.test_results.append({
                'test': 'device_fingerprint',
                'passed': False
            })
            return False
    
    def test_url_enhancement(self):
        """测试URL参数增强"""
        print("\n=== Testing URL Enhancement ===")
        
        try:
            base_url = "https://xueqiu.com/statuses/hot/listV3.json?source=hot&page=1"
            
            # 模拟URL增强
            enhanced_params = {
                'seq': str(random.randint(1, 1000)),
                'fp': hashlib.md5(str(time.time()).encode()).hexdigest()[:16],
                'ts': str(int(time.time() * 1000)),
                'r': str(random.random())[:8]
            }
            
            param_str = '&'.join([f'{k}={v}' for k, v in enhanced_params.items()])
            enhanced_url = f"{base_url}&{param_str}"
            
            if len(enhanced_url) > len(base_url) and all(param in enhanced_url for param in enhanced_params.keys()):
                print(f"✓ URL enhanced successfully")
                print(f"  Original length: {len(base_url)}")
                print(f"  Enhanced length: {len(enhanced_url)}")
                self.test_results.append({
                    'test': 'url_enhancement',
                    'passed': True
                })
                return True
            else:
                print("✗ URL enhancement failed")
                return False
                
        except Exception as e:
            print(f"✗ URL enhancement test failed: {e}")
            self.test_results.append({
                'test': 'url_enhancement',
                'passed': False
            })
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 Starting Xueqiu Anti-Crawling Solution Tests")
        print("=" * 50)
        
        # 加载JavaScript环境
        if not self.load_js_context():
            print("❌ Cannot proceed without JavaScript context")
            return False
        
        # 运行所有测试
        tests = [
            self.test_acw_sc_v2_generation,
            self.test_anti_debug_bypass,
            self.test_device_fingerprint,
            self.test_url_enhancement
        ]
        
        passed_tests = 0
        for test in tests:
            if test():
                passed_tests += 1
        
        # 输出测试结果
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)
        
        for result in self.test_results:
            status = "✓ PASS" if result['passed'] else "✗ FAIL"
            test_name = result['test'].replace('_', ' ').title()
            print(f"{status} {test_name}")
            if 'success_rate' in result:
                print(f"    Success Rate: {result['success_rate']:.1f}%")
        
        overall_success = (passed_tests / len(tests)) * 100
        print(f"\n🎯 Overall Success Rate: {overall_success:.1f}% ({passed_tests}/{len(tests)})")
        
        if overall_success >= 75:
            print("🎉 Anti-crawling solution is working well!")
            return True
        else:
            print("⚠️  Anti-crawling solution needs improvement")
            return False

def main():
    """主函数"""
    tester = XueqiuAntiCrawlingTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n✅ All tests completed successfully!")
        exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        exit(1)

if __name__ == '__main__':
    main()
